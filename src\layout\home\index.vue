<template>
  <div class="home">
    <!-- 按钮集合 -->
    <section class="buttons">
      <span> 正在装修：{{ storeName }}</span>
      <div>
        <el-button @click="exit" class="exit">退出</el-button>
        <el-button @click="push(true)">保存</el-button>
        <el-button @click="push(false)" type="primary">发布</el-button>
        <!-- <el-button @click="reloads" type="danger">重置</el-button> -->
        <!-- <el-button @click="realTimeViewData.show = true">预览</el-button> -->
        <!-- <el-button @click="goLogin">预览</el-button> -->
        <!-- <el-button @click="catJson">查看 JSON </el-button> -->
        <!-- <el-button @click="$refs.file.click()">导入 JSON </el-button> -->
        <!-- <el-button @click="exportJSON">导出 JSON </el-button> -->
        <input
          type="file"
          ref="file"
          id="file"
          accept=".json"
          @change="importJSON"
          style="display: none"
        />
      </div>
    </section>

    <!-- 装修操作 -->
    <section class="operation">
      <!-- 组件 -->
      <sliderassembly :pointer="pointer" />

      <!-- 手机 -->
      <div class="phone">
        <section class="phoneAll" ref="imageTofile" id="imageTofile">
          <img src="@/assets/images/phoneTop.jpg" alt="" class="statusBar" />

          <!-- 头部导航 -->
          <headerTop :pageSetup="pageSetup" @click="headTop" />

          <!-- 主体内容 -->
          <section
            class="phone-container"
            :style="{
              'background-color': pageSetup.bgColor,
              backgroundImage: 'url(' + pageSetup.bgImg + ')',
            }"
            @drop="drop($event)"
            @dragover="allowDrop($event)"
            @dragleave="dragleaves($event)"
          >
            <!-- 动态组件 -->
            <vuedraggable
              :class="pointer.show ? 'pointer-events comBox' : 'comBox'"
              :list="pageComponents"
              item-key="index"
              :forceFallback="true"
              :animation="200"
              @end="handleDragEnd"
            >
              <template #item="{ element, index }">
                <component
                  :is="element.component"
                  :datas="element.setStyle"
                  @click="activeComponent(element, index)"
                  class="componentsClass"
                  :class="{ active: element.active && deleShow }"
                  :data-type="element.type"
                >
                  <template #deles>
                    <div
                      v-show="deleShow"
                      class="deles"
                      @click.stop="deleteObj(index)"
                    >
                      <!-- 删除组件 -->
                      <span class="iconfont icon-sanjiaoxingzuo"></span>
                      {{ element.text }}
                      <van-icon name="delete" />
                    </div>
                  </template>
                </component>
              </template>
            </vuedraggable>
          </section>

          <!-- 手机高度 -->
          <!-- <div class="phoneSize">iPhone 8 手机高度</div> -->

          <!-- 底部 -->
          <!-- <phoneBottom /> -->
        </section>
        <!-- 底部 -->
      </div>

      <!-- 页面设置 tab -->
      <!-- <div class="decorateTab">
        <span
          :class="rightcom === 'decorate' ? 'active' : ''"
          @click="rightcom = 'decorate'"
        >
          <i class="iconfont icon-wangye" />
          页面设置
        </span>
        <span
          :class="rightcom === 'componenmanagement' ? 'active' : ''"
          @click="rightcom = 'componenmanagement'"
        >
          <i class="iconfont icon-zujian" />
          组件管理
        </span>
        <span
          class="active"
          v-show="rightcom != 'componenmanagement' && rightcom != 'decorate'"
        >
          <i class="iconfont icon-zujian" />
          组件设置
        </span>
      </div> -->

      <div class="layoutBox">
        <el-collapse v-model="compActive">
          <el-collapse-item title="页面布局" name="1">
            <div class="content">
              <Componenmanagement
                :datas="datas.pageComponents"
                :current-selected-index="currentComponentIndex"
                @componenmanagement="componenmanagement"
                @activeComponent="handleComponentManagementClick"
                @update-selected-index="updateSelectedIndex"
              />
            </div>
          </el-collapse-item>
        </el-collapse>
        <div
          class="set"
          @click="handlePageSettingClick"
          :class="{ active: isPageSettingActive }"
        >
          <!-- 添加激活状态类 -->
          <el-icon><Setting /></el-icon>
          <el-button link> 页面设置 </el-button>
        </div>
      </div>

      <!-- 右侧工具栏 -->
      <div class="decorateAll">
        <!-- 页面设置 -->
        <rightslidercom
          :title="
            (Array.isArray(currentproperties) && '组件管理') ||
            { articlemanagestyle: '文章管理' }[rightcom] ||
            currentproperties.text ||
            '页面设置'
          "
        >
          <!-- 动态组件 -->
          <component
            :key="choose.index"
            :is="rightcom"
            :datas="currentproperties"
            @componenmanagement="componenmanagement"
            @activeComponent="activeComponent"
          />
        </rightslidercom>
      </div>
    </section>
    <!-- <realTimeView
      :datas="realTimeViewData"
      :val="{
        id,
        name: pageSetup.name,
        templateJson: JSON.stringify(pageSetup),
        component: JSON.stringify(pageComponents),
      }"
    /> -->
  </div>
</template>

<script setup>
import api from '@/api/api'
import headerTop from '@/components/headerTop'
import componentProperties from '@/utils/componentProperties' // 组件数据
import Componenmanagement from '@/components/rightslider/componenmanagement'
import { ElMessage, ElMessageBox } from 'element-plus'
import FileSaver from 'file-saver' // 导出 JSON
import utils from 'utils/index' // 方法类
import { inject, reactive, ref, toRefs, watch } from 'vue'
import vuedraggable from 'vuedraggable' //拖拽组件
import template from './template.json'
import { Setting } from '@element-plus/icons-vue'

const isPageSettingActive = ref(false)
const currentComponentIndex = ref(-1)
// URL 传参
const obj = {}
const queryString = location.href.split('?')[1]
if (queryString) {
  queryString.split('&').map((item) => {
    const [key, value] = item.split('=')
    obj[key] = value
  })
}
const typeObj = { add: '新增', edit: '编辑', copy: '新增' }
const compActive = ref('1')
const type = ref(obj.type)
const storeName = ref()
let message = null
// 页面数据
const datas = reactive({
  id: null, // 当前页面 id
  pageSetup: {
    name: '', // 页面名称
    details: '', // 页面描述
    bgColor: 'rgba(249, 249, 249, 10)', // 背景颜色
  }, // 页面设置属性
  pageComponents: [], // 页面组件
})
// 选择组件数据
const choose = reactive({
  deleShow: true, // 删除标签显示
  index: '', // 当前选中的 index
  rightcom: 'decorate', // 右侧组件切换
  currentproperties: datas.pageSetup, // 当前属性  默认：页面设置
  offsetY: 0, //记录上一次距离父元素高度
  onlyOne: [], // 只能存在一个的组件 (组件的 type)
  pointer: { show: false }, // 穿透
})
const handlePageSettingClick = () => {
  rightcom.value = 'decorate' // 保持原有逻辑：切换到页面设置面板
  isPageSettingActive.value = true // 激活按钮背景色

  clearComponentSelection()
}

// 新增：清除组件选中状态的方法
const clearComponentSelection = () => {
  // 清除画布上的选中状态
  datas.pageComponents.forEach((component) => {
    component.active = false
  })

  // 清除全局选中索引
  currentComponentIndex.value = -1

  // 重置右侧面板到页面设置
  choose.rightcom = 'decorate'
  choose.currentproperties = datas.pageSetup
}

// 新增：组件管理项点击事件（用于重置页面设置按钮状态）
const handleComponentManagementClick = (element, index) => {
  activeComponent(element, index)
  isPageSettingActive.value = false
}
// 加载数据
const init = (id) => {
  // 模板回显
  if (obj.template && obj.template != 0 && template[obj.template].pageSetup) {
    datas.pageSetup = template[obj.template].pageSetup
    datas.pageComponents = template[obj.template].pageComponents
    choose.currentproperties = template[obj.template].pageSetup
  }
  api.storeInfo(obj.storeId).then((res) => {
    storeName.value = res.data.name
  })
  if (!id) return
  api.info({ id }).then((res) => {
    let ImportJSON = JSON.parse(res.data.pageContent)
    datas.pageSetup = ImportJSON.pageSetup
    datas.pageComponents = ImportJSON.pageComponents
    datas.id = res.data.id
    datas.pageState = res.data.pageState
    datas.pageName = res.data.pageName
    choose.currentproperties = ImportJSON.pageSetup
    if (
      type == 'edit' &&
      res.data.pageState == 2 &&
      res.data.pageDraftState == 1
    ) {
      message = ElMessage({
        showClose: true,
        dangerouslyUseHTMLString: true,
        message: `<div>
          <span>该页面有一条草稿记录，可以还原草稿继续编辑。</span>
          <button class="el-button el-button--default" type="button" style="margin: 0 10px;" onclick="next()">继续编辑</button>
        </div>`,
        type: 'warning',
        duration: 0,
      })
    }
  })
}
init(obj.pageId)

// 继续编辑
window.next = () => {
  api.checkInfo({ id: datas.id }).then((res) => {
    let ImportJSON = JSON.parse(res.data.pageContent)
    datas.pageSetup = ImportJSON.pageSetup
    datas.pageComponents = ImportJSON.pageComponents
    // datas.id = res.data.id
    datas.pageState = res.data.pageState
    datas.pageName = res.data.pageName
    choose.currentproperties = ImportJSON.pageSetup
    message.close()
  })
}

// 退出
const exit = () => {
  location.href = `${process.env.VUE_APP_EXCHANGE_URL}business/my-shop?storeId=${obj.storeId}&tabKey=shopFit`
}

// 发布/保存/编辑
const push = (e) => {
  if (!(datas.pageSetup.name && datas.pageSetup.details)) {
    ElMessage.warning('请完善页面设置')
    choose.rightcom = 'decorate'
    return
  }
  const { pageSetup, pageComponents } = datas
  // console.log({ pageSetup, pageComponents })
  // return
  if (obj.type == 'edit') {
    // 编辑状态
    api
      .update({
        id: datas.id,
        pageState: datas.pageState,
        pageContent: JSON.stringify({ pageSetup, pageComponents }),
        pageName: datas.pageSetup.name,
        remark: datas.pageSetup.details,
        updateOrPublish: e, // { false: '无草稿', true: '草稿' }
      })
      .then((res) => {
        if (res.code == 20000) {
          ElMessage.success(e ? '已保存' : '已发布')
          e || setTimeout(exit, 500)
        } else {
          ElMessage.warning(e ? '保存失败' : '发布失败')
        }
      })
  } else {
    // 新建状态
    api
      .push({
        storeId: obj.storeId,
        pageContent: JSON.stringify({ pageSetup, pageComponents }),
        pageName: datas.pageSetup.name,
        remark: datas.pageSetup.details,
        updateOrPublish: e, // { false: '无草稿', true: '草稿' }
      })
      .then((res) => {
        if (res.code == 20000) {
          ElMessage.success(e ? '已保存' : '已发布')
          if (e) {
            obj.type = 'edit'
            datas.id = res.data
            history.pushState(
              null,
              null,
              `${location.href.split('&')[0]}&pageId=${res.data}&type=edit`
            )
          } else {
            setTimeout(exit, 500)
          }
        } else {
          ElMessage.warning(e ? '保存失败' : '发布失败')
        }
      })
  }
}

// 是否显示预览
// const realTimeViewData = reactive({ show: false })

// 查看 JSON
const catJson = () => {
  ElMessageBox.alert(
    `{
          <br/>
          "id": ${datas.id},
          <br/>
          "name": "${datas.pageSetup.name}",
          <br/>
          "templateJson": '${JSON.stringify(datas.pageSetup)}',
          <br/>
          "component": '${JSON.stringify(datas.pageComponents)}',
          <br/>
        }`,
    '查看 JSON',
    {
      confirmButtonText: '确定',
      customClass: 'JSONView',
      dangerouslyUseHTMLString: true,
      callback: () => {},
    }
  )
}

// 导出 json
const exportJSON = () => {
  // 将 json 转换成字符串
  const data = JSON.stringify({
    id: datas.id,
    name: datas.pageSetup.name,
    templateJson: JSON.stringify(datas.pageSetup),
    component: JSON.stringify(datas.pageComponents),
  })
  const blob = new Blob([data], { type: '' })
  FileSaver.saveAs(blob, `${datas.pageSetup.name}.json`)
}

// 导入 json
const importJSON = () => {
  const file = document.getElementById('file').files[0]
  const reader = new FileReader()
  reader.readAsText(file)
  let _this = datas
  reader.onload = function () {
    // this.result 为读取到的 json 字符串，需转成 json 对象
    let ImportJSON = JSON.parse(this.result)
    // 检测是否导入成功
    console.log(ImportJSON, '-----------------导入成功')
    // 导入 JSON 数据
    _this.id = ImportJSON.id
    _this.pageSetup = JSON.parse(ImportJSON.templateJson)
    _this.pageComponents = JSON.parse(ImportJSON.component)
  }
}

// 编辑
const edit = () => {}

/**
 * 切换组件位置  用于组件管理中删除功能
 *
 * @param {Object} res 组件切换后返回的位置
 */
// 修改：处理拖拽排序
const componenmanagement = (res) => {
  datas.pageComponents = res

  // 重新计算选中组件的索引
  const activeIndex = res.findIndex((item) => item.active)
  currentComponentIndex.value = activeIndex

  // 如果找到了选中组件，更新右侧面板
  if (activeIndex !== -1) {
    choose.rightcom = res[activeIndex].style
    choose.currentproperties = res[activeIndex].setStyle
  }
}

// 新增：处理拖拽结束事件
const handleDragEnd = (evt) => {
  const { oldIndex, newIndex } = evt

  // 清除所有组件的选中状态
  datas.pageComponents.forEach((component) => {
    component.active = false
  })

  // 如果拖拽的是当前选中的组件，更新索引并保持选中状态
  if (currentComponentIndex.value === oldIndex) {
    currentComponentIndex.value = newIndex

    // 仅在有效索引范围内设置选中状态
    if (newIndex !== -1 && datas.pageComponents[newIndex]) {
      datas.pageComponents[newIndex].active = true
      choose.rightcom = datas.pageComponents[newIndex].style
      choose.currentproperties = datas.pageComponents[newIndex].setStyle
    } else {
      // 如果索引无效，重置右侧面板到页面设置
      choose.rightcom = 'decorate'
      choose.currentproperties = datas.pageSetup
    }
  } else if (oldIndex !== newIndex) {
    // 如果拖拽的是未选中组件，确保它不被选中
    if (newIndex !== -1 && datas.pageComponents[newIndex]) {
      datas.pageComponents[newIndex].active = false
    }
    // 如果原来有选中组件，重新设置其选中状态
    if (
      currentComponentIndex.value !== -1 &&
      datas.pageComponents[currentComponentIndex.value]
    ) {
      datas.pageComponents[currentComponentIndex.value].active = true
    }
  }
}

// 新增：处理子组件拖拽结束事件
const updateSelectedIndex = (newIndex) => {
  currentComponentIndex.value = newIndex

  // 更新右侧面板
  if (newIndex !== -1 && datas.pageComponents[newIndex]) {
    choose.rightcom = datas.pageComponents[newIndex].style
    choose.currentproperties = datas.pageComponents[newIndex].setStyle
  }
}

/**
 * 选择组件
 *
 * @param {Object} res 当前组件对象
 */
const activeComponent = (res, index) => {
  choose.index = index
  currentComponentIndex.value = index // 更新全局索引
  /* 切换组件 */
  choose.rightcom = res.style
  /* 丢样式 */
  choose.currentproperties = res.setStyle
  console.log(8888888, res)
  /* 替换 */
  datas.pageComponents.forEach((res) => {
    /* 修改选中 */
    if (res.active === true) res.active = false
  })

  /* 选中样式 */
  res.active = true
}

// 切换标题
const headTop = () => {
  choose.rightcom = 'decorate'
  /* 替换 */
  datas.pageComponents.forEach((res) => {
    /* 修改选中 */
    if (res.active === true) res.active = false
  })
}

/**
 * 删除组件
 *
 * @param {Number} index 当前组件 index
 */
const deleteObj = (index) => {
  datas.pageComponents.splice(index, 1)
  // 更新全局索引
  if (currentComponentIndex.value === index) {
    currentComponentIndex.value = -1
    choose.rightcom = 'decorate' // 切换到页面设置
  } else if (index < currentComponentIndex.value) {
    currentComponentIndex.value -= 1
  }
}

/**
 * 当将元素或文本选择拖动到有效放置目标（每几百毫秒）上时，会触发此事件
 *
 * @param {Object} event event对象
 */
const allowDrop = (event) => {
  //阻止浏览器的默认事件
  event.preventDefault()

  /* 获取鼠标高度 */
  let eventoffset = event.offsetY

  /* 如果没有移动不触发事件减少损耗 */
  if (choose.offsetY === eventoffset) return
  else choose.offsetY = eventoffset

  /* 获取组件 */
  const childrenObject = event.target.children[0]

  // 一个以上的组件计算
  if (datas.pageComponents.length) {
    /* 如果只有一个组件并且第一个是提示组件直接返回 */
    if (datas.pageComponents.length === 1 && datas.pageComponents[0].type === 0)
      return

    /* 如果鼠标的高度小于第一个的一半直接放到第一个 */
    if (eventoffset < childrenObject.children[0].clientHeight / 2) {
      /* 如果第一个是提示组件直接返回 */
      if (datas.pageComponents[0].type === 0) return

      /* 删除提示组件 */
      datas.pageComponents = datas.pageComponents.filter(
        (res) => res.component !== 'placementarea'
      )

      /* 最后面添加提示组件 */
      datas.pageComponents.unshift({
        component: 'placementarea',
        type: 0,
      })

      return
    }

    /* 记录距离父元素高度 */
    const childOff = childrenObject.offsetTop

    /* 鼠标在所有组件下面 */
    if (
      eventoffset > childrenObject.clientHeight ||
      childrenObject.lastChild.offsetTop -
        childOff +
        childrenObject.lastChild.clientHeight / 2 <
        eventoffset
    ) {
      /* 最后一个组件是提示组件返回 */
      if (datas.pageComponents[datas.pageComponents.length - 1].type === 0)
        return

      /* 清除提示组件 */
      datas.pageComponents = datas.pageComponents.filter(
        (res) => res.component !== 'placementarea'
      )

      /* 最后一个不是提示组件添加 */
      datas.pageComponents.push({
        component: 'placementarea',
        type: 0,
      })

      return
    }

    const childrens = childrenObject.children

    /* 在两个组件中间，插入 */
    for (let i = 0, l = childrens.length; i < l; i++) {
      const childoffset = childrens[i].offsetTop - childOff

      if (childoffset + childrens[i].clientHeight / 2 > event.offsetY) {
        /* 如果是提示组件直接返回 */
        if (datas.pageComponents[i].type === 0) break

        if (datas.pageComponents[i - 1].type === 0) break

        /* 清除提示组件 */
        datas.pageComponents = datas.pageComponents.filter(
          (res) => res.component !== 'placementarea'
        )

        datas.pageComponents.splice(i, 0, {
          component: 'placementarea',
          type: 0,
        })
        break
      } else if (childoffset + childrens[i].clientHeight > event.offsetY) {
        if (datas.pageComponents[i].type === 0) break

        if (
          !datas.pageComponents[i + 1] ||
          datas.pageComponents[i + 1].type === 0
        )
          break

        datas.pageComponents = datas.pageComponents.filter(
          (res) => res.component !== 'placementarea'
        )

        datas.pageComponents.splice(i, 0, {
          component: 'placementarea',
          type: 0,
        })

        break
      }
    }
  } else {
    /* 一个组件都没有直接 push */
    datas.pageComponents.push({
      component: 'placementarea',
      type: 0,
    })
  }
}

/**
 * 当在有效放置目标上放置元素或选择文本时触发此事件
 *
 * @param {Object} event event 对象
 */
const drop = (event) => {
  /* 获取数据 */
  let data = utils.deepClone(
    componentProperties.get(event.dataTransfer.getData('componentName'))
  )

  /* 查询是否只能存在一个的组件且在第一个 */
  let someOne = datas.pageComponents.some((item, index) => {
    return (
      item.component === 'placementarea' &&
      index === 0 &&
      choose.onlyOne.includes(data.type)
    )
  })
  if (someOne) {
    ElMessage.info('固定位置的组件 (如：底部导航、悬浮) 不能放在第一个！')
    /* 删除提示组件 */
    dragleaves()
    return
  }

  /* 查询是否只能存在一个的组件 */
  let someResult = datas.pageComponents.some((item) => {
    return (
      choose.onlyOne.includes(item.type) &&
      item.component === event.dataTransfer.getData('componentName')
    )
  })
  if (someResult) {
    ElMessage.info('当前组件只能添加一个！')
    /* 删除提示组件 */
    dragleaves()
    return
  }

  /* 替换 */
  datas.pageComponents.forEach((res, index) => {
    /* 修改选中 */
    if (res.active === true) res.active = false
    /* 替换提示 */
    if (res.component === 'placementarea') {
      datas.pageComponents[index] = data

      // 新增：选中新添加的组件
      data.active = true
      currentComponentIndex.value = index

      /* 切换组件 */
      choose.rightcom = data.style
      /* 丢样式 */
      choose.currentproperties = data.setStyle
    }
  })

  /* 切换组件 */
  choose.rightcom = data.style
  /* 丢样式 */
  choose.currentproperties = data.setStyle

  // console.log(
  //   data,
  //   choose.rightcom,
  //   choose.currentproperties,
  //   '----------components data'
  // )
}

/**
 * 当拖动的元素或文本选择离开有效的放置目标时，会触发此事件
 *
 * @param {Object} event event 对象
 */
const dragleaves = () => {
  /* 删除提示组件 */
  datas.pageComponents = datas.pageComponents.filter(
    (res) => res.component !== 'placementarea'
  )
}

const reload = inject('reload')
// 重置
const reloads = () => {
  ElMessageBox.confirm(
    '重置后您添加或者修改的数据将会失效，是否继续？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      reload()
    })
    .catch(() => {})
}

// 监听右侧属性设置切换
watch(
  () => choose.rightcom,
  (newval) => {
    if (newval === 'decorate') {
      datas.pageComponents.forEach((res) => {
        /* 修改选中 */
        if (res.active === true) res.active = false
      })
      choose.currentproperties = datas.pageSetup
      return
    }
    if (newval === 'componenmanagement') {
      /* 替换 */
      datas.pageComponents.forEach((res) => {
        /* 修改选中 */
        if (res.active === true) res.active = false
      })
      choose.currentproperties = datas.pageComponents
    }
  }
)

const { id, pageSetup, pageComponents } = toRefs(datas)
const { deleShow, rightcom, currentproperties, pointer } = toRefs(choose)
</script>

<style lang="less" scoped>
.pointer-events {
  pointer-events: none;
}
.comBox {
  display: flex;
  flex-direction: column;
  // gap: 10px;
}

.home {
  width: 100%;
  height: 100%;
  user-select: none;
  /* 删除组件 */
  .deles {
    position: absolute;
    min-width: 80px;
    text-align: center;
    line-height: 25px;
    background: #fff;
    height: 25px;
    font-size: 12px;
    left: 103%;
    top: 50%;
    transform: translateY(-50%);
    .icon-sanjiaoxingzuo {
      position: absolute;
      left: -11px;
      color: #fff;
      font-size: 12px;
      top: 50%;
      transform: translateY(-50%);
    }
    &:hover {
      i {
        display: block;
        position: absolute;
        left: 0;
        font-size: 16px;
        top: 0;
        text-align: center;
        line-height: 25px;
        width: 100%;
        color: #fff;
        height: 100%;
        z-index: 10;
        background: rgba(0, 0, 0, 0.5);
      }
      .icon-sanjiaoxingzuo {
        color: rgba(0, 0, 0, 0.5);
      }
    }

    i {
      display: none;
    }
  }

  /* 按钮集合 */
  .buttons {
    height: 48px;
    background: #000;
    box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    > span {
      font-size: 20px;
      color: rgba(255, 255, 255, 0.85);
    }
    button {
      border: none;
    }
    /* 下拉 */
    .frop {
      padding-right: 15px;
      .el-button.el-button--primary.el-dropdown-selfdefine {
        background: #fff;
        color: #000;
        border: 1px solid #dcdee0;
      }
    }
    .el-button {
      font-size: 14px;
      padding: 0 16px;
      height: 30px;
      // &.el-button--primary {
      //   background: #1890FF;
      // }
      // &.el-button--danger {
      //   background: red;
      // }
    }
    .exit:hover {
      color: red;
    }
  }

  /* 操作主体 */
  .operation {
    width: 100%;
    height: calc(100% - 48px);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background: #f7f8fa;
  }

  /* 手机 */
  .phone {
    flex: 1;
    height: 100%;
    overflow-y: scroll;
    display: flex;
    justify-content: center;
    background: #f7f8fa;

    /* 手机样式 */
    .phoneAll {
      width: 375px;
      min-height: 760px;
      height: fit-content;
      box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.1);
      margin: 45px 0;
      position: relative;
      display: flex;
      flex-direction: column;

      /* 手机高度 */
      .phoneSize {
        position: absolute;
        left: -137px;
        top: 640px;
        font-size: 12px;
        color: #a2a2a2;
        border-bottom: 1px solid #dedede;
        width: 130px;
        height: 21px;
        line-height: 21px;
      }

      /* 状态栏 */
      .statusBar {
        width: 100%;
        display: block;
      }

      /* 主体内容 */
      .phone-container {
        flex: 1;
        // min-height: 603px;
        box-sizing: border-box;
        cursor: pointer;
        width: 100%;
        position: relative;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        .componentsClass {
          &:hover {
            &::after {
              content: '';
              position: absolute;
              inset: 0;
              border: 1px dashed #1890ff;
            }
          }
        }
        .active::after {
          content: '';
          position: absolute;
          inset: 0;
          border: 1px solid #1890ff;
        }
      }
    }
  }

  /* 右侧工具栏 */
  .decorateAll {
    width: 370px;
    height: 100%;
    background: #fff;
    border-left: 1px solid #ebedf0;
    box-sizing: content-box;
    // 自定义样式
    :deep(.el-slider__runway.show-input) {
      margin-right: 20px;
    }
    :deep(.el-slider__input) {
      width: 120px;
    }
  }

  /* 页面设置 tab */
  .decorateTab {
    position: fixed;
    display: flex;
    right: 390px;
    top: 115px;
    flex-direction: column;
    span {
      background-color: #fff;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      width: 94px;
      height: 32px;
      display: inline-block;
      text-align: center;
      line-height: 32px;
      margin-bottom: 12px;
      transition: all 0.8s;
      cursor: pointer;
      &.active {
        background-color: #1890ff;
        color: #fff;
      }
      /* 图标 */
      i {
        font-size: 12px;
        margin-right: 5px;
      }
    }
  }
  .layoutBox {
    height: 100%;
    // background: #fff;
    position: relative;
    width: 200px;
    /deep/.el-collapse-item__title {
      padding-left: 12px !important;
    }
    .content {
      padding: 0 12px 32px;
      height: calc(100vh - 124px);
      overflow-y: auto;
    }
    .set {
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      border-top: 1px solid #ccc;
      padding: 15px 10px;
      display: flex;
      align-items: center;
      background: #fff;
      &.active {
        background-color: #e6f7ff; // 激活时的背景色（可根据需求调整）
        border-radius: 4px; // 可选：添加圆角增强视觉效果
      }
    }
  }
}

/* 动画 */
.decorateAnima-enter-active {
  transition: all 1.5s ease;
}
.decorateAnima-leave-active {
  transition: all 1.5s ease;
}
.decorateAnima-enter {
  transform: translate(8px, 8px);
  opacity: 0;
}
.decorateAnima-leave-to {
  transform: translate(8px, 8px);
  opacity: 0;
}
</style>

<style>
::-webkit-scrollbar {
  width: 0;
}
</style>
