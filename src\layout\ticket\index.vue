<template>
  <div
    class="home"
    @mousemove="onMousemove"
    @mouseup="onMouseup"
    @keydown.enter="onKey"
  >
    <!-- 按钮集合 -->
    <section class="buttons">
      <span> 编辑门票模板</span>
      <div>
        <el-button @click="exit" class="exit">退出</el-button>
        <el-button type="primary" @click="push(true)">保存</el-button>
      </div>
    </section>

    <!-- 装修操作 -->
    <section class="operation">
      <!-- 组件 -->
      <ticketcomponent :pointer="pointer" @selectCom="selectCom" />

      <!-- 手机 -->
      <div class="phone">
        <section
          @mouseup="onSetCom"
          class="phoneAll"
          :style="{
            width: pageSetup.width + 'mm',
            height: pageSetup.height - 1 + 'mm',
            background: pageSetup.bgUrl
              ? `url(${pageSetup.bgUrl}) no-repeat center`
              : '#fff',
            backgroundSize:
              pageSetup.fillType === 'full' ? '100% 100%' : 'contain',
            overflow: 'hidden',
          }"
          ref="imageTofile"
          id="imageTofile"
        >
          <div
            v-for="(element, index) in pageComponents"
            :key="index"
            @click="activeComponent(element, index)"
            @mousedown="onMoveComponent($event, 'start', index)"
            @mousemove="onMoveComponent($event, 'move', index)"
            @mouseup="onMoveComponent($event, 'end', index)"
            @mouseleave="onMoveComponent($event, 'end', index)"
            :style="{
              top: element.setStyle.top + 'mm',
              left: element.setStyle.left + 'mm',
              zIndex: element.active ? 1 : 0,
              backgroundColor: element.active
                ? 'rgba(0, 0, 0, 0.5)'
                : 'transparent',
            }"
            class="pageComponent"
          >
            <component :is="element.name" :datas="element.setStyle" />
            <!-- <div v-show="element.active" class="activeCom"> -->
            <div class="close" @click="onDel(index)">
              <img src="@/assets/images/close.png" alt="" />
            </div>
            <!-- </div> -->
          </div>
        </section>
      </div>

      <!-- 基础设置 tab -->
      <div class="decorateTab">
        <span
          :class="rightcom === 'ticketdecorate' ? 'active' : ''"
          @click="rightcom = 'ticketdecorate'"
        >
          <i class="iconfont icon-wangye" />
          基础设置
        </span>
        <span
          :class="rightcom === 'componenmanagement' ? 'active' : ''"
          @click="rightcom = 'componenmanagement'"
        >
          <i class="iconfont icon-zujian" />
          组件管理
        </span>
        <span
          class="active"
          v-show="
            rightcom != 'componenmanagement' && rightcom != 'ticketdecorate'
          "
        >
          <i class="iconfont icon-zujian" />
          组件设置
        </span>
      </div>

      <!-- 右侧工具栏 -->
      <div class="decorateAll">
        <!-- 基础设置 -->
        <rightslidercom
          :title="
            (Array.isArray(currentproperties) && '组件管理') ||
            { articlemanagestyle: '文章管理' }[rightcom] ||
            currentproperties.text ||
            '基础设置'
          "
        >
          <!-- 动态组件 -->
          <component
            :key="choose.index"
            :is="rightcom"
            :datas="currentproperties"
            :tableData="tableData"
            :pageSetup="pageSetup"
            @componenmanagement="componenmanagement"
            @activeComponent="activeComponent"
          />
        </rightslidercom>
      </div>
    </section>
    <!-- <realTimeView
      :datas="realTimeViewData"
      :val="{
        id,
        name: pageSetup.name,
        templateJson: JSON.stringify(pageSetup),
        component: JSON.stringify(pageComponents),
      }"
    /> -->

    <div
      v-show="sCom.show"
      :style="sStyle"
      style="position: absolute; pointer-events: none"
    >
      <component
        :is="sCom.name"
        :datas="{
          ...sCom.style,
          tableData: sCom.tableData,
        }"
        class="componentsClass"
      >
      </component>
    </div>
  </div>
</template>

<script setup>
import api from '@/api/api'
import componentProperties from '@/utils/componentProperties' // 组件数据
import { ElMessage } from 'element-plus'
import html2canvas from 'html2canvas'
import utils from 'utils/index' // 方法类
import { onMounted, onUnmounted, reactive, ref, toRefs, watch } from 'vue'
import { useRoute } from 'vue-router'

// 移动单位
const moveUnit = 1

const imageTofile = ref(null)

const uploadImage = async (imageBlob) => {
  const formData = new FormData()
  formData.append('file', imageBlob, 'image.png')
  const res = await api.uploadImg(formData)
  if (res.code !== 20000) {
    ElMessage.error('上传失败')
    return
  }
  return res.data.path
}

function pxToMm(px) {
  var dpi = window.devicePixelRatio * 96
  return (px * 25.4) / dpi
}

let isMoveCom = false
// 拖拽组件
const onMoveComponent = (e, type, index) => {
  e.preventDefault()
  if (type === 'start') {
    isMoveCom = true
  }
  if (type === 'end') {
    isMoveCom = false
  }
  if (type === 'move' && isMoveCom) {
    console.log('拖拽', e, index)
    datas.pageComponents[index].setStyle.left += pxToMm(e.movementX)
    datas.pageComponents[index].setStyle.top += pxToMm(e.movementY)
  }
}

const tableData = ref([
  {
    key: 'ticketNumber',
    title: '票号',
    showTitle: false,
    fontSize: 4,
    value: '15343822796883927062022000000001',
    show: true,
    disabled: true,
  },

  {
    key: 'totalPrice',
    title: '票价',
    showTitle: false,
    fontSize: 5,
    value: '¥100.00',
    show: true,
    disabled: true,
  },
  {
    key: 'scenicName',
    title: '景区名称',
    showTitle: false,
    fontSize: 4,
    value: '俊进科技园',
    show: true,
    disabled: false,
  },
  {
    key: 'proName',
    title: '产品名称',
    showTitle: false,
    fontSize: 4,
    value: '入园门票',
    show: true,
    disabled: false,
  },
  {
    key: 'goodsName',
    title: '商品名称',
    showTitle: false,
    fontSize: 4,
    value: '个人票',
    show: true,
    disabled: false,
  },
  {
    key: 'ticketPrice',
    title: '单价',
    showTitle: false,
    fontSize: 5,
    value: '¥50.00',
    show: false,
    disabled: true,
  },
  {
    key: 'productType',
    title: '产品类型',
    showTitle: false,
    fontSize: 4,
    value: '门票',
    show: false,
    disabled: false,
  },
  {
    key: 'ticketType',
    title: '商品票种',
    showTitle: false,
    fontSize: 4,
    value: '成人票',
    show: false,
    disabled: false,
  },
  {
    key: 'ticketNum',
    title: '人数',
    showTitle: false,
    fontSize: 4,
    value: '5 人',
    show: false,
    disabled: false,
  },
  {
    key: 'name',
    title: '姓名/身份证',
    showTitle: false,
    fontSize: 4,
    value: '张*/110************001',
    show: false,
    disabled: false,
  },
  {
    key: 'admissionDate',
    title: '入园日期',
    showTitle: false,
    fontSize: 4,
    value: '2024 年 1 月 1 日',
    show: false,
    disabled: false,
  },
  {
    key: 'admissionTime',
    title: '分时预约',
    showTitle: false,
    fontSize: 4,
    value: '8:00~12:00',
    show: false,
    disabled: false,
  },
  {
    key: 'validityPeriod',
    title: '可入园天数',
    showTitle: false,
    fontSize: 4,
    value: '5 天',
    show: false,
    disabled: false,
  },
  {
    key: 'admissionTimes',
    title: '入园次数',
    showTitle: false,
    fontSize: 4,
    value: '每天 5 次',
    show: false,
    disabled: false,
  },
  // {
  //   key: 'admissionNum',
  //   title: '上链 ID',
  //   showTitle: false,
  //   fontSize: 4,
  //   value: '0x4c4baf42a3c6ac3fcle430d0380a59d025df32d1f39f3f2ce93bd221b05a0070',
  //   show: false,
  //   disabled: false,
  // },
  // {
  //   key: 'transactionHash',
  //   title: '交易哈希',
  //   showTitle: false,
  //   fontSize: 4,
  //   value: '0x4cfa7114e085688541d90972a5725b2765bbb47dofd692cd86427d1df3e63bd1',
  //   show: false,
  //   disabled: false,
  // },
  {
    key: 'tagline',
    title: '提示宣传语',
    isTagline: true,
    showTitle: false,
    fontSize: 4,
    value: '',
    show: false,
    disabled: false,
  },
])

const keydownHandler = (e) => {
  console.log('keydownHandler', e)
  // 监听方向键
  if (e.key === 'Delete') {
    console.log('删除')
    datas.pageComponents.splice(choose.index, 1)
  }
  if (e.key === 'ArrowUp') {
    if (choose.index >= 0) {
      datas.pageComponents[choose.index].setStyle.top -= moveUnit
    }
  }
  if (e.key === 'ArrowDown') {
    if (choose.index >= 0) {
      datas.pageComponents[choose.index].setStyle.top += moveUnit
    }
  }
  if (e.key === 'ArrowLeft') {
    if (choose.index >= 0) {
      datas.pageComponents[choose.index].setStyle.left -= moveUnit
    }
  }
  if (e.key === 'ArrowRight') {
    if (choose.index >= 0) {
      datas.pageComponents[choose.index].setStyle.left += moveUnit
    }
  }
}

const onDel = (index) => {
  // console.log('sCom.show')
  // console.log(sCom)

  // console.log(datas.pageComponents[index].setStyle === sCom.style)
  // console.log('eeee')
  // if (datas.pageComponents[index].setStyle === sCom.style) {
  //   sCom.style = {}
  //   sCom.name = ''
  //   sCom.show = false
  //   console.log('onnnnnnnnnnnnn')
  // }

  datas.pageComponents.splice(index, 1)
}

const route = useRoute()
const {
  scenicId,
  scenicName,
  id: templateId,
  code: uniqueIdentity,
} = route.query
onMounted(() => {
  // 获取门票模板信息
  if (templateId) {
    api.getTicketInfo(templateId).then((res) => {
      console.log(res)
      const { templateInfo, templateName, id } = res.data
      const { pageSetup, pageComponents } = JSON.parse(templateInfo || '{}')
      console.log(pageSetup, pageComponents)
      datas.pageComponents = pageComponents
      datas.pageSetup = pageSetup
      datas.id = templateId
      choose.currentproperties = pageSetup
    })
  }

  document.title = '编辑门票模板'
  window.addEventListener('keydown', keydownHandler)

  tableData.value.forEach((item) => {
    if (item.key === 'scenicName') item.value = scenicName
  })
})
onUnmounted(() => {
  window.removeEventListener('keydown', keydownHandler)
})

// URL 传参
const obj = {}
const queryString = location.href.split('?')[1]
if (queryString) {
  queryString.split('&').map((item) => {
    const [key, value] = item.split('=')
    obj[key] = value
  })
}
let message = null

// 页面数据
const datas = reactive({
  id: null, // 当前页面 id
  pageSetup: {
    name: '', // 页面名称
    width: 150, // 页面宽度
    height: 70, // 页面高度
    fillType: 'contain', // 背景填充方式
  }, // 基础设置属性
  pageComponents: [], // 页面组件
})
// 选择组件数据
const choose = reactive({
  deleShow: true, // 删除标签显示
  index: '', // 当前选中的 index
  rightcom: 'ticketdecorate', // 右侧组件切换
  currentproperties: datas.pageSetup, // 当前属性  默认：基础设置
  offsetY: 0, //记录上一次距离父元素高度
  onlyOne: ['1-2'], // 只能存在一个的组件 (组件的 type)
  pointer: { show: false }, // 穿透
})

const sCom = reactive({
  name: '',
  show: false,
  style: {},
})
const sStyle = ref({
  left: '0px',
  top: '0px',
})

const onMouseup = () => {
  sCom.show = false
  sCom.name = ''
}

const onKey = () => {
  console.log('onKey')
}

const onMousemove = (e) => {
  // 获取鼠标位置
  if (sCom.name) {
    if (!sCom.show) sCom.show = true
    sStyle.value = {
      left: e.clientX + 'px',
      top: e.clientY + 'px',
    }
  }
}
const selectCom = (com, name) => {
  if (!com) return
  sCom.name = com
  let data = utils.deepClone(componentProperties.get(sCom.name))
  sCom.style = {
    ...data.setStyle,
  }
  // 如果是文本区域
  if (sCom.name === 'textblock') {
    // 已经被选中的字段
    const disableList = []
    datas.pageComponents
      .filter((item, i) => {
        return item.name === 'textblock'
      })
      .forEach((item) => {
        item.setStyle.tableData.forEach((item) => {
          if (item.show) disableList.push(item.title)
        })
      })
    let num = 0
    sCom.tableData = tableData.value.map((item) => {
      if (disableList.includes(item.title)) {
        item.show = false
        item.disabled = true
      } else {
        if (num < 1) {
          item.show = true
          num++
        }
      }
      return item
    })
    // 如果全部都被选中，不可拖拽
    if (num === 0) {
      ElMessage.info('当前组件已全部添加！')
      sCom.show = false
      return
    }
  }
}

// 确认组件
const onSetCom = (e) => {
  if (!sCom.show) return

  /* 查询是否只能存在一个的组件 */
  let someResult = datas.pageComponents.some((item) => {
    return choose.onlyOne.includes(item.type) && item.component === sCom.name
  })
  if (someResult) {
    ElMessage.info('当前组件只能添加一个！')
    /* 删除提示组件 */
    return
  }

  console.log('确认组件', sCom.tableData)
  const { offsetX, offsetY } = e

  let data = utils.deepClone(componentProperties.get(sCom.name))
  datas.pageComponents.push({
    name: sCom.name,
    ...data,
    setStyle: {
      ...data.setStyle,
      top: pxToMm(offsetY),
      left: pxToMm(offsetX),
      tableData: sCom.tableData ? utils.deepClone(sCom.tableData) : [],
    },
  })
  console.log(datas.pageComponents)
}

// 加载数据
const init = (id) => {}
init(obj.pageId)

// 退出
const exit = () => {
  location.href = `${process.env.VUE_APP_SCENIC_URL}${uniqueIdentity}/basic-information/config`
}

// 发布/保存/编辑
const push = async (e) => {
  console.log('datas', datas)
  console.log(datas, scenicId)
  const { pageComponents, pageSetup } = datas
  if (!pageSetup.name) {
    ElMessage.error('请填写模板名称')
    return
  }

  // 清空选中样式
  datas.pageComponents.forEach((res) => {
    /* 修改选中 */
    if (res.active === true) res.active = false
  })

  const canvas = await html2canvas(imageTofile.value, {
    useCORS: true,
  })
  // 生成的 canvas 可以转换为图片
  canvas.toBlob(async (blob) => {
    try {
      // 现在 blob 包含了图片数据，可以将其上传到服务器
      const templateUrl = await uploadImage(blob)
      if (templateId) {
        // 编辑
        const res = await api.updateTicket({
          id: templateId,
          scenicId,
          templateInfo: JSON.stringify(datas),
          templateName: pageSetup.name,
          templateUrl,
        })
      } else {
        // 新增
        await api.saveTicket({
          scenicId,
          templateInfo: JSON.stringify(datas),
          templateName: pageSetup.name,
          templateUrl,
        })
      }
      ElMessage.success('保存成功')
      setTimeout(() => {
        location.href = `${process.env.VUE_APP_SCENIC_URL}${uniqueIdentity}/basic-information/config`
      }, 500)
    } catch (e) {
      console.log(e)
      ElMessage.error('保存失败')
    }
  }, 'image/png')
}
/**
 * 切换组件位置  用于组件管理中删除功能
 *
 * @param {Object} res 组件切换后返回的位置
 */
const componenmanagement = (res) => {
  datas.pageComponents = res
}

/**
 * 选择组件
 *
 * @param {Object} res 当前组件对象
 */
const activeComponent = (res, index) => {
  console.log('激活组件', datas.pageComponents, res, index)

  // 设置文本区域数据
  if (res.name === 'textblock') {
    // 已经被选中的字段
    const disableList = []
    datas.pageComponents
      .filter((item, i) => {
        return item.name === 'textblock' && index !== i
      })
      .forEach((item) => {
        item.setStyle.tableData.forEach((item) => {
          if (item.show) disableList.push(item.title)
        })
      })
    // 已选中的字段，不可再次选中
    res.setStyle.tableData.forEach((item) => {
      if (disableList.includes(item.title)) {
        item.show = false
        item.disabled = true
      } else {
        item.disabled = false
      }
    })
  }

  choose.index = index
  /* 切换组件 */
  choose.rightcom = res.style
  /* 丢样式 */
  choose.currentproperties = res.setStyle
  console.log(8888888, res)
  /* 替换 */
  datas.pageComponents.forEach((res) => {
    /* 修改选中 */
    if (res.active === true) res.active = false
  })

  /* 选中样式 */
  res.active = true
}

// 监听右侧属性设置切换
watch(
  () => choose.rightcom,
  (newval) => {
    if (newval === 'ticketdecorate') {
      datas.pageComponents.forEach((res) => {
        /* 修改选中 */
        if (res.active === true) res.active = false
      })
      choose.currentproperties = datas.pageSetup
      return
    }
    if (newval === 'componenmanagement') {
      /* 替换 */
      datas.pageComponents.forEach((res) => {
        /* 修改选中 */
        if (res.active === true) res.active = false
      })
      choose.currentproperties = datas.pageComponents
    }
  }
)

const { id, pageSetup, pageComponents } = toRefs(datas)
const { deleShow, rightcom, currentproperties, pointer } = toRefs(choose)
</script>

<style lang="less" scoped>
.pointer-events {
  pointer-events: none;
}
.comBox {
  display: flex;
  flex-direction: column;
  // gap: 10px;
}

.home {
  width: 100%;
  height: 100%;
  user-select: none;
  /* 删除组件 */
  .deles {
    position: absolute;
    min-width: 80px;
    text-align: center;
    line-height: 25px;
    background: #fff;
    height: 25px;
    font-size: 12px;
    left: 103%;
    top: 50%;
    transform: translateY(-50%);
    .icon-sanjiaoxingzuo {
      position: absolute;
      left: -11px;
      color: #fff;
      font-size: 12px;
      top: 50%;
      transform: translateY(-50%);
    }
    &:hover {
      i {
        display: block;
        position: absolute;
        left: 0;
        font-size: 16px;
        top: 0;
        text-align: center;
        line-height: 25px;
        width: 100%;
        color: #fff;
        height: 100%;
        z-index: 10;
        background: rgba(0, 0, 0, 0.5);
      }
      .icon-sanjiaoxingzuo {
        color: rgba(0, 0, 0, 0.5);
      }
    }

    i {
      display: none;
    }
  }

  /* 按钮集合 */
  .buttons {
    height: 48px;
    background: #000;
    box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    > span {
      font-size: 20px;
      color: rgba(255, 255, 255, 0.85);
    }
    button {
      border: none;
    }
    /* 下拉 */
    .frop {
      padding-right: 15px;
      .el-button.el-button--primary.el-dropdown-selfdefine {
        background: #fff;
        color: #000;
        border: 1px solid #dcdee0;
      }
    }
    .el-button {
      font-size: 14px;
      padding: 0 16px;
      height: 30px;
      // &.el-button--primary {
      //   background: #1890FF;
      // }
      // &.el-button--danger {
      //   background: red;
      // }
    }
    .exit:hover {
      color: red;
    }
  }

  /* 操作主体 */
  .operation {
    width: 100%;
    height: calc(100% - 48px);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background: #f7f8fa;
  }

  /* 手机 */
  .phone {
    flex: 1;
    height: 100%;
    overflow-y: scroll;
    display: flex;
    justify-content: center;
    background: #f7f8fa;

    /* 手机样式 */
    .phoneAll {
      width: 600px;
      height: 250px;
      height: fit-content;
      box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.1);
      margin: 45px 0;
      position: relative;
      display: flex;
      flex-direction: column;

      /* 手机高度 */
      .phoneSize {
        position: absolute;
        left: -137px;
        top: 640px;
        font-size: 12px;
        color: #a2a2a2;
        border-bottom: 1px solid #dedede;
        width: 130px;
        height: 21px;
        line-height: 21px;
      }

      /* 状态栏 */
      .statusBar {
        width: 100%;
        display: block;
      }

      /* 主体内容 */
      .phone-container {
        flex: 1;
        // min-height: 603px;
        box-sizing: border-box;
        cursor: pointer;
        width: 100%;
        position: relative;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        .componentsClass {
          &:hover {
            &::after {
              content: '';
              position: absolute;
              inset: 0;
              border: 1px dashed #1890ff;
            }
          }
        }
        .active::after {
          content: '';
          position: absolute;
          inset: 0;
          border: 1px solid #1890ff;
        }
      }
    }
  }

  /* 右侧工具栏 */
  .decorateAll {
    width: 370px;
    height: 100%;
    background: #fff;
    border-left: 1px solid #ebedf0;
    box-sizing: content-box;
    // 自定义样式
    :deep(.el-slider__runway.show-input) {
      margin-right: 20px;
    }
    :deep(.el-slider__input) {
      width: 120px;
    }
  }

  /* 基础设置 tab */
  .decorateTab {
    position: fixed;
    display: flex;
    right: 390px;
    top: 115px;
    flex-direction: column;
    span {
      background-color: #fff;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      width: 94px;
      height: 32px;
      display: inline-block;
      text-align: center;
      line-height: 32px;
      margin-bottom: 12px;
      transition: all 0.8s;
      cursor: pointer;
      &.active {
        background-color: #1890ff;
        color: #fff;
      }
      /* 图标 */
      i {
        font-size: 12px;
        margin-right: 5px;
      }
    }
  }
}

/* 动画 */
.decorateAnima-enter-active {
  transition: all 1.5s ease;
}
.decorateAnima-leave-active {
  transition: all 1.5s ease;
}
.decorateAnima-enter {
  transform: translate(8px, 8px);
  opacity: 0;
}
.decorateAnima-leave-to {
  transform: translate(8px, 8px);
  opacity: 0;
}

.pageComponent {
  position: absolute;
  cursor: move;
  &:hover {
    background-color: rgba(0, 0, 0, 0.5) !important;
    .close {
      display: block;
    }
  }
  .close {
    display: none;
    position: absolute;
    right: -10px;
    top: -10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    background-color: #fff;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>

<style>
::-webkit-scrollbar {
  width: 0;
}
</style>
